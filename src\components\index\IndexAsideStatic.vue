<template>
  <el-aside class="index-aside" height="100vh" :width="sidebarCollapsed ? '64px' : '210px'" :class="{'collapsed': sidebarCollapsed}">
    <div class="index-aside-inner menulist" style="height:100%">
      <div v-for="item in menuList" :key="item.roleName" v-if="role==item.roleName" class="menulist-item" style="height:100%;broder:0;background-color:#1E90FF">
        <div class="menulistImg" style="backgroundColor:#269B9E;padding:30px 0" v-if="false && menulistStyle == 'vertical'">
          <el-image v-if="'http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg'" src="http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg" fit="cover" />
        </div>
        <el-menu mode="vertical" :unique-opened="true" :collapse="sidebarCollapsed" class="el-menu-demo custom-menu" style="height:100%;" background-color="#1E90FF" text-color="#ffffff" active-text-color="#FFFFFF" default-active="0">
          <el-menu-item index="(0).toString()" :style="menulistBorderBottom" @click="menuHandler('')" class="menu-item-enhanced"><i v-if="true" class="el-icon-s-home" />首页</el-menu-item>
          <el-submenu :index="(1).toString()" :style="menulistBorderBottom">
            <template slot="title">
              <i v-if="true" class="el-icon-user-solid" />
              <span>个人中心</span>
            </template>
            <el-menu-item :index="(1-1).toString()" @click="menuHandler('updatePassword')" class="menu-item-enhanced">修改密码</el-menu-item>
            <el-menu-item :index="(1-2).toString()" @click="menuHandler('center')" class="menu-item-enhanced">个人信息</el-menu-item>
          </el-submenu>
          <el-submenu :style="menulistBorderBottom" v-for=" (menu,index) in item.backMenu" :key="menu.menu" :index="(index+2).toString()">
            <template slot="title">
              <i v-if="true" :class="icons[index]" />
              <span>{{ menu.menu }}</span>
            </template>
            <el-menu-item v-for=" (child,sort) in menu.child" :key="sort" :index="((index+2)+'-'+sort).toString()" @click="menuHandler(child.tableName)" class="menu-item-enhanced">{{ child.menu }}</el-menu-item>
          </el-submenu>
        </el-menu>

      </div>
    </div>
  </el-aside>
</template>
<script>
import menu from '@/utils/menu'
export default {
  data() {
    return {
      menuList: [],
      dynamicMenuRoutes: [],
      role: '',
      sidebarCollapsed: false,
      icons: [
        'el-icon-s-cooperation',
        'el-icon-s-order',
        'el-icon-s-platform',
        'el-icon-s-fold',
        'el-icon-s-unfold',
        'el-icon-s-operation',
        'el-icon-s-promotion',
        'el-icon-s-release',
        'el-icon-s-ticket',
        'el-icon-s-management',
        'el-icon-s-open',
        'el-icon-s-shop',
        'el-icon-s-marketing',
        'el-icon-s-flag',
        'el-icon-s-comment',
        'el-icon-s-finance',
        'el-icon-s-claim',
        'el-icon-s-custom',
        'el-icon-s-opportunity',
        'el-icon-s-data',
        'el-icon-s-check',
        'el-icon-s-grid',
        'el-icon-menu',
        'el-icon-chat-dot-square',
        'el-icon-message',
        'el-icon-postcard',
        'el-icon-position',
        'el-icon-microphone',
        'el-icon-close-notification',
        'el-icon-bangzhu',
        'el-icon-time',
        'el-icon-odometer',
        'el-icon-crop',
        'el-icon-aim',
        'el-icon-switch-button',
        'el-icon-full-screen',
        'el-icon-copy-document',
        'el-icon-mic',
        'el-icon-stopwatch',
      ],
      menulistStyle: 'vertical',
	  menulistBorderBottom: {},
    }
  },
  mounted() {
    const menus = menu.list()
    this.menuList = menus
    this.role = this.$storage.get('role')

    // 监听侧边栏收缩事件
    this.$root.$on('sidebar-toggle', (collapsed) => {
      this.sidebarCollapsed = collapsed
    })
  },
  created(){
    setTimeout(()=>{
      this.menulistStyleChange()
    },10)
    this.icons.sort(()=>{
      return (0.5-Math.random())
    })
	this.lineBorder()
  },
  methods: {
	lineBorder() {
		let style = 'vertical'
		let w = '1px'
		let s = 'solid'
		let c = '#ccc'
		if(style == 'vertical') {
			this.menulistBorderBottom = {
				borderBottomWidth: w,
				borderBottomStyle: s,
				borderBottomColor: c
			}
		} else {
			this.menulistBorderBottom = {
				borderRightWidth: w,
				borderRightStyle: s,
				borderRightColor: c
			}
		}
	},
    menuHandler(name) {
      let router = this.$router
      name = '/'+name
      router.push(name).catch(err => err)
    },
    // 菜单
    setMenulistHoverColor(){
      let that = this
      this.$nextTick(()=>{
        document.querySelectorAll('.menulist .el-menu-item').forEach(el=>{
          el.addEventListener("mouseenter", e => {
            e.stopPropagation()
            el.style.backgroundColor = "rgba(101, 126, 253, 1)"
          })
          el.addEventListener("mouseleave", e => {
            e.stopPropagation()
            el.style.backgroundColor = "#1E90FF"
          })
          el.addEventListener("focus", e => {
            e.stopPropagation()
            el.style.backgroundColor = "rgba(101, 126, 253, 1)"
          })
        })
        document.querySelectorAll('.menulist .el-submenu__title').forEach(el=>{
          el.addEventListener("mouseenter", e => {
            e.stopPropagation()
            el.style.backgroundColor = "rgba(101, 126, 253, 1)"
          })
          el.addEventListener("mouseleave", e => {
            e.stopPropagation()
            el.style.backgroundColor = "#1E90FF"
          })
        })
      })
    },
    setMenulistIconColor() {
      this.$nextTick(()=>{
        document.querySelectorAll('.menulist .el-submenu__title .el-submenu__icon-arrow').forEach(el=>{
          el.style.color = "rgba(153, 153, 153, 1)"
        })
      })
    },
    menulistStyleChange() {
      this.setMenulistIconColor()
      this.setMenulistHoverColor()
      this.setMenulistStyleHeightChange()
      let str = "vertical"
      if("horizontal" === str) {
        this.$nextTick(()=>{
          document.querySelectorAll('.el-container .el-container').forEach(el=>{
            el.style.display = "block"
            el.style.paddingTop = "60px" // header 高度
          })
          document.querySelectorAll('.el-aside').forEach(el=>{
            el.style.width = "100%"
            el.style.height = "60px"
            el.style.paddingTop = '0'
          })
          document.querySelectorAll('.index-aside .index-aside-inner').forEach(el=>{
            el.style.paddingTop = '0'
          })
        })
      }
      if("vertical" === str) {
        this.$nextTick(()=>{
          document.querySelectorAll('.index-aside .index-aside-inner').forEach(el=>{
            el.style.paddingTop = "60px"
          })
        })
      }
    },
    setMenulistStyleHeightChange() {
      this.$nextTick(()=>{
        document.querySelectorAll('.menulist-item>.el-menu--horizontal>.el-menu-item').forEach(el=>{
          el.style.height = "60px"
          el.style.lineHeight = "60px"
        })
        document.querySelectorAll('.menulist-item>.el-menu--horizontal>.el-submenu>.el-submenu__title').forEach(el=>{
          el.style.height = "60px"
          el.style.lineHeight = "60px"
        })
      })
    },
  }
}
</script>
<style lang="scss" scoped>
  .index-aside {
    position: relative;
    overflow: hidden;
    background: linear-gradient(180deg, #1E90FF 0%, #4169E1 50%, #0066CC 100%);
    box-shadow: 2px 0 8px rgba(30, 144, 255, 0.2);
    transition: width 0.3s ease;

    &.collapsed {
      .index-aside-inner {
        .custom-menu {
          .el-menu-item,
          .el-submenu {
            .el-submenu__title {
              padding: 0 20px !important;
              text-align: center;

              span {
                display: none;
              }
            }
          }

          .el-menu-item {
            padding: 0 20px !important;
            text-align: center;

            span {
              display: none;
            }
          }
        }
      }
    }

    .menulistImg {
      padding: 24px 0;
      box-sizing: border-box;

      .el-image {
        margin: 0 auto;
        width: 100px;
        height: 100px;
        border-radius: 100%;
        display: block;
        border: 3px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }

    .index-aside-inner {
      height: 100%;
      margin-right: -17px;
      margin-bottom: -17px;
      overflow: scroll;
      overflow-x: hidden !important;
      padding-top: 60px;
      box-sizing: border-box;

      &:focus {
        outline: none;
      }

      .el-menu {
        border: 0;
        background: transparent !important;
      }

      // 自定义菜单样式
      .custom-menu {
        .el-menu-item {
          position: relative;
          margin: 2px 8px;
          border-radius: 8px;
          transition: all 0.3s ease;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

          &:hover {
            background: rgba(255, 255, 255, 0.15) !important;
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          }

          &.is-active {
            background: rgba(255, 255, 255, 0.2) !important;
            color: #fff !important;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 60%;
              background: #fff;
              border-radius: 0 2px 2px 0;
            }
          }

          i {
            margin-right: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
          }

          &:hover i {
            transform: scale(1.1);
          }
        }

        .el-submenu {
          margin: 2px 8px;
          border-radius: 8px;

          .el-submenu__title {
            border-radius: 8px;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

            &:hover {
              background: rgba(255, 255, 255, 0.15) !important;
              transform: translateX(4px);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }

            i {
              margin-right: 8px;
              font-size: 16px;
              transition: all 0.3s ease;
            }

            &:hover i {
              transform: scale(1.1);
            }
          }

          .el-menu {
            background: rgba(0, 0, 0, 0.1) !important;

            .el-menu-item {
              padding-left: 50px !important;
              font-size: 14px;

              &:hover {
                background: rgba(255, 255, 255, 0.1) !important;
              }

              &.is-active {
                background: rgba(255, 255, 255, 0.15) !important;
                position: relative;

                &::before {
                  left: 20px;
                  width: 3px;
                  height: 50%;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
