{"name": "mas-creator-admin", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.2", "core-js": "^3.4.4", "echarts": "^4.6.0", "element-ui": "^2.13.0", "js-md5": "^0.7.3", "vue-qr": "^3.2.2", "print-js": "^1.5.0", "vue": "^2.6.10", "vue-quill-editor": "^3.0.6", "vue-amap": "^0.5.10", "vue-json-excel": "^0.3.0", "vue-router": "^3.1.5"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-service": "^4.1.0", "babel-eslint": "^10.0.3", "babel-plugin-component": "^1.1.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "node-sass": "^4.13.1", "sass-loader": "^8.0.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"no-console": "off", "no-unused-vars": 0, "no-useless-escape": "off"}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8", "Android >= 4.0"]}