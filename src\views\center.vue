<template>
  <div>
    <el-form
      class="detail-form-content"
      ref="ruleForm"
      :model="ruleForm"
      label-width="80px"
    >  
     <el-row>
                    <el-col :span="12">
           <el-form-item v-if="flag=='shangjia'"  label='商家名称' prop="shangjiaName">
               <el-input v-model="ruleForm.shangjiaName"  placeholder='商家名称' clearable></el-input>
           </el-form-item>
         </el-col>

         <el-col :span="12">
           <el-form-item v-if="flag=='shangjia'"  label='联系方式' prop="shangjiaPhone">
               <div class="input-with-privacy">
                  <template v-if="showEdits.shangjiaPhone">
                    <el-input v-model="ruleForm.shangjiaPhone" placeholder='联系方式' clearable></el-input>
                    <i class="el-icon-check edit-icon" @click="toggleEdit('shangjiaPhone')"></i>
                    <i class="el-icon-close edit-icon" @click="cancelEdit('shangjiaPhone')"></i>
                  </template>
                  <div v-else class="privacy-display">
                    <privacy-info :value="ruleForm.shangjiaPhone" type="phone"></privacy-info>
                    <i class="el-icon-edit edit-icon" @click="toggleEdit('shangjiaPhone')"></i>
                  </div>
               </div>
           </el-form-item>
         </el-col>

         <el-col :span="12">
           <el-form-item v-if="flag=='shangjia'"  label='邮箱' prop="shangjiaEmail">
               <div class="input-with-privacy">
                  <template v-if="showEdits.shangjiaEmail">
                    <el-input v-model="ruleForm.shangjiaEmail" placeholder='邮箱' clearable></el-input>
                    <i class="el-icon-check edit-icon" @click="toggleEdit('shangjiaEmail')"></i>
                    <i class="el-icon-close edit-icon" @click="cancelEdit('shangjiaEmail')"></i>
                  </template>
                  <div v-else class="privacy-display">
                    <privacy-info :value="ruleForm.shangjiaEmail" type="email"></privacy-info>
                    <i class="el-icon-edit edit-icon" @click="toggleEdit('shangjiaEmail')"></i>
                  </div>
               </div>
           </el-form-item>
         </el-col>

         <el-col :span="12">
             <el-form-item v-if="flag=='shangjia'" label='营业执照展示' prop="shangjiaPhoto">
                 <file-upload
                         tip="点击上传照片"
                         action="file/upload"
                         :limit="3"
                         :multiple="true"
                         :fileUrls="ruleForm.shangjiaPhoto?ruleForm.shangjiaPhoto:''"
                         @change="shangjiaPhotoUploadChange"
                 ></file-upload>
             </el-form-item>
         </el-col>
         <el-col :span="12">
           <el-form-item v-if="flag=='shangjia'"  label='商店地址' prop="shangjiaAddress">
               <el-input v-model="ruleForm.shangjiaAddress"  placeholder='商店地址' clearable></el-input>
           </el-form-item>
         </el-col>

         <el-col :span="12">
             <el-form-item v-if="flag=='shangjia'"  label='商家信用类型' prop="shangjiaXingjiTypes">
                 <el-select v-model="ruleForm.shangjiaXingjiTypes" disabled placeholder='请选择商家信用类型'>
                     <el-option
                             v-for="(item,index) in shangjiaXingjiTypesOptions"
                             v-bind:key="item.codeIndex"
                             :label="item.indexName"
                             :value="item.codeIndex">
                     </el-option>
                 </el-select>
             </el-form-item>
         </el-col>
         <el-col :span="12">
             <el-form-item v-if="flag=='shangjia'"  label='商家状态' prop="shangjiaYesnoTypes">
                 <el-select v-model="ruleForm.shangjiaYesnoTypes" disabled  placeholder='请选择商家状态'>
                     <el-option
                             v-for="(item,index) in shangjiaYesnoTypesOptions"
                             v-bind:key="item.codeIndex"
                             :label="item.indexName"
                             :value="item.codeIndex">
                     </el-option>
                 </el-select>
             </el-form-item>
         </el-col>
         <el-col :span="12">
             <el-form-item v-if="flag=='shangjia'"  label='现有余额' prop="newMoney">
                 <el-input v-model="ruleForm.newMoney"  placeholder='现有余额' disabled ></el-input>
             </el-form-item>
         </el-col>
         <el-col :span="24">
             <el-form-item v-if="flag=='shangjia'"  label="商家简介" prop="shangjiaContent">
                 <editor
                         style="min-width: 200px; max-width: 600px;"
                         v-model="ruleForm.shangjiaContent"
                         class="editor"
                         action="file/upload">
                 </editor>
             </el-form-item>
         </el-col>
         <el-col :span="12">
           <el-form-item v-if="flag=='yonghu'"  label='用户姓名' prop="yonghuName">
               <el-input v-model="ruleForm.yonghuName"  placeholder='用户姓名' clearable></el-input>
           </el-form-item>
         </el-col>

         <el-col :span="12">
           <el-form-item v-if="flag=='yonghu'"  label='用户手机号' prop="yonghuPhone">
               <div class="input-with-privacy">
                  <template v-if="showEdits.yonghuPhone">
                    <el-input v-model="ruleForm.yonghuPhone" placeholder='用户手机号' clearable></el-input>
                    <i class="el-icon-check edit-icon" @click="toggleEdit('yonghuPhone')"></i>
                    <i class="el-icon-close edit-icon" @click="cancelEdit('yonghuPhone')"></i>
                  </template>
                  <div v-else class="privacy-display">
                    <privacy-info :value="ruleForm.yonghuPhone" type="phone"></privacy-info>
                    <i class="el-icon-edit edit-icon" @click="toggleEdit('yonghuPhone')"></i>
                  </div>
               </div>
           </el-form-item>
         </el-col>

         <el-col :span="12">
           <el-form-item v-if="flag=='yonghu'"  label='用户身份证号' prop="yonghuIdNumber">
               <div class="input-with-privacy">
                  <template v-if="showEdits.yonghuIdNumber">
                    <el-input v-model="ruleForm.yonghuIdNumber" placeholder='用户身份证号' clearable></el-input>
                    <i class="el-icon-check edit-icon" @click="toggleEdit('yonghuIdNumber')"></i>
                    <i class="el-icon-close edit-icon" @click="cancelEdit('yonghuIdNumber')"></i>
                  </template>
                  <div v-else class="privacy-display">
                    <privacy-info :value="ruleForm.yonghuIdNumber" type="idcard"></privacy-info>
                    <i class="el-icon-edit edit-icon" @click="toggleEdit('yonghuIdNumber')"></i>
                  </div>
               </div>
           </el-form-item>
         </el-col>

         <el-col :span="12">
             <el-form-item v-if="flag=='yonghu'" label='用户头像' prop="yonghuPhoto">
                 <file-upload
                         tip="点击上传照片"
                         action="file/upload"
                         :limit="3"
                         :multiple="true"
                         :fileUrls="ruleForm.yonghuPhoto?ruleForm.yonghuPhoto:''"
                         @change="yonghuPhotoUploadChange"
                 ></file-upload>
             </el-form-item>
         </el-col>
         <el-col :span="12">
           <el-form-item v-if="flag=='yonghu'"  label='电子邮箱' prop="yonghuEmail">
               <div class="input-with-privacy">
                  <template v-if="showEdits.yonghuEmail">
                    <el-input v-model="ruleForm.yonghuEmail" placeholder='电子邮箱' clearable></el-input>
                    <i class="el-icon-check edit-icon" @click="toggleEdit('yonghuEmail')"></i>
                    <i class="el-icon-close edit-icon" @click="cancelEdit('yonghuEmail')"></i>
                  </template>
                  <div v-else class="privacy-display">
                    <privacy-info :value="ruleForm.yonghuEmail" type="email"></privacy-info>
                    <i class="el-icon-edit edit-icon" @click="toggleEdit('yonghuEmail')"></i>
                  </div>
               </div>
           </el-form-item>
         </el-col>

         <el-col :span="12">
             <el-form-item v-if="flag=='yonghu'"  label='余额' prop="newMoney">
                 <el-input v-model="ruleForm.newMoney"  placeholder='余额' disabled ></el-input>
             </el-form-item>
         </el-col>
         <el-form-item v-if="flag=='users'" label="用户名" prop="username">
             <el-input v-model="ruleForm.username"
                       placeholder="用户名"></el-input>
         </el-form-item>
         <el-col :span="12">
             <el-form-item v-if="flag!='users' && flag!='shangjia'"  label="性别" prop="sexTypes">
                 <el-select v-model="ruleForm.sexTypes" placeholder="请选择性别">
                     <el-option
                             v-for="(item,index) in sexTypesOptions"
                             v-bind:key="item.codeIndex"
                             :label="item.indexName"
                             :value="item.codeIndex">
                     </el-option>
                 </el-select>
             </el-form-item>
         </el-col>
         <el-col :span="24">
             <el-form-item>
                 <el-button type="primary" @click="onUpdateHandler">修 改</el-button>
             </el-form-item>
         </el-col>
     </el-row>
    </el-form>
  </div>
</template>
<script>
// 数字，邮件，手机，url，身份证校验
import { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from "@/utils/validate";

export default {
  data() {
    return {
      ruleForm: {},
      originalValues: {},
      flag: '',
      usersFlag: false,
      sexTypesOptions : [],
     shangjiaXingjiTypesOptions : [],
     shangjiaYesnoTypesOptions : [],
     showEdits: {
       shangjiaPhone: false,
       shangjiaEmail: false,
       yonghuPhone: false,
       yonghuIdNumber: false,
       yonghuEmail: false
     }
    };
  },
  mounted() {
    //获取当前登录用户的信息
    var table = this.$storage.get("sessionTable");
    this.sessionTable = this.$storage.get("sessionTable");
    this.role = this.$storage.get("role");
    if (this.role != "管理员"){
    }

    this.flag = table;
    this.$http({
      url: `${this.$storage.get("sessionTable")}/session`,
      method: "get"
    }).then(({ data }) => {
      if (data && data.code === 0) {
        this.ruleForm = data.data;
      } else {
        this.$message.error(data.msg);
      }
    });
      this.$http({
          url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=sex_types`,
          method: "get"
      }).then(({ data }) => {
          if (data && data.code === 0) {
          this.sexTypesOptions = data.data.list;
      } else {
          this.$message.error(data.msg);
      }
  });
   this.$http({
       url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangjia_xingji_types`,
       method: "get"
   }).then(({ data }) => {
       if (data && data.code === 0) {
          this.shangjiaXingjiTypesOptions = data.data.list;
      } else {
          this.$message.error(data.msg);
      }
    });
   this.$http({
       url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangjia_yesno_types`,
       method: "get"
   }).then(({ data }) => {
       if (data && data.code === 0) {
          this.shangjiaYesnoTypesOptions = data.data.list;
      } else {
          this.$message.error(data.msg);
      }
    });
  },
  methods: {
    shangjiaPhotoUploadChange(fileUrls) {
        this.ruleForm.shangjiaPhoto = fileUrls;
    },
    yonghuPhotoUploadChange(fileUrls) {
        this.ruleForm.yonghuPhoto = fileUrls;
    },

    onUpdateHandler() {
                         if((!this.ruleForm.shangjiaName)&& 'shangjia'==this.flag){
                             this.$message.error('商家名称不能为空');
                             return
                         }

                         if((!this.ruleForm.shangjiaPhone)&& 'shangjia'==this.flag){
                             this.$message.error('联系方式不能为空');
                             return
                         }

                             if( 'shangjia' ==this.flag && this.ruleForm.shangjiaEmail&&(!isEmail(this.ruleForm.shangjiaEmail))){
                                 this.$message.error(`邮箱应输入邮箱格式`);
                                 return
                             }
                         if((!this.ruleForm.shangjiaPhoto)&& 'shangjia'==this.flag){
                             this.$message.error('营业执照展示不能为空');
                             return
                         }

                         if((!this.ruleForm.shangjiaAddress)&& 'shangjia'==this.flag){
                             this.$message.error('商店地址不能为空');
                             return
                         }

                         if((!this.ruleForm.shangjiaXingjiTypes)&& 'shangjia'==this.flag){
                             this.$message.error('商家信用类型不能为空');
                             return
                         }

                         if((!this.ruleForm.shangjiaYesnoTypes)&& 'shangjia'==this.flag){
                             this.$message.error('商家状态不能为空');
                             return
                         }

                         if((!this.ruleForm.shangjiaContent)&& 'shangjia'==this.flag){
                             this.$message.error('商家简介不能为空');
                             return
                         }

                         if((!this.ruleForm.yonghuName)&& 'yonghu'==this.flag){
                             this.$message.error('用户姓名不能为空');
                             return
                         }

                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuPhone&&(!isMobile(this.ruleForm.yonghuPhone))){
                                 this.$message.error(`手机应输入手机格式`);
                                 return
                             }
                         if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.flag){
                             this.$message.error('用户身份证号不能为空');
                             return
                         }

                         if((!this.ruleForm.yonghuPhoto)&& 'yonghu'==this.flag){
                             this.$message.error('用户头像不能为空');
                             return
                         }

                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuEmail&&(!isEmail(this.ruleForm.yonghuEmail))){
                                 this.$message.error(`邮箱应输入邮箱格式`);
                                 return
                             }
        if((!this.ruleForm.sexTypes) && this.flag!='users' && this.flag!='shangjia'){
            this.$message.error('性别不能为空');
            return
        }
      if('users'==this.flag && this.ruleForm.username.trim().length<1) {
        this.$message.error(`用户名不能为空`);
        return	
      }
      this.$http({
        url: `${this.$storage.get("sessionTable")}/update`,
        method: "post",
        data: this.ruleForm
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: "修改信息成功",
            type: "success",
            duration: 1500,
            onClose: () => {
            }
          });
        } else {
          this.$message.error(data.msg);
        }
      });
    },
    toggleEdit(field) {
      if (!this.showEdits[field]) {
        this.originalValues[field] = this.ruleForm[field];
      }
      this.showEdits[field] = !this.showEdits[field];
    },
    cancelEdit(field) {
      if (this.originalValues[field] !== undefined) {
        this.ruleForm[field] = this.originalValues[field];
      }
      this.showEdits[field] = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.input-with-privacy {
  display: flex;
  align-items: center;
}

.input-with-privacy .privacy-info {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 15px;
  height: 40px;
  line-height: 40px;
  background-color: #fff;
}

.privacy-display {
  display: flex;
  align-items: center;
  width: 100%;
}

.edit-icon {
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px;
}

.el-icon-edit {
  color: #409EFF;
}

.el-icon-check {
  color: #67C23A;
}

.el-icon-close {
  color: #F56C6C;
  margin-left: 5px;
}
</style>
