<template>
	<el-main class="main-content">
		<div class="content-header">
			<bread-crumbs :title="title" class="bread-crumbs"></bread-crumbs>
			<div class="page-actions">
				<el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshContent">刷新</el-button>
			</div>
		</div>
		<div class="content-body">
			<router-view class="router-view"></router-view>
		</div>
	</el-main>
</template>
<script>
	import menu from "@/utils/menu";
	export default {
		data() {
			return {
				menuList: [],
				role: "",
				currentIndex: -2,
				itemMenu: [],
				title: ''
			};
		},
		mounted() {
			let menus = menu.list();
			this.menuList = menus;
			this.role = this.$storage.get("role");
		},
		methods: {
			menuHandler(menu) {
				this.$router.push({
					name: menu.tableName
				});
				this.title = menu.menu;
			},
			titleChange(index, menus) {
				this.currentIndex = index
				this.itemMenu = menus;
				console.log(menus);
			},
			homeChange(index) {
				this.itemMenu = [];
				this.title = ""
				this.currentIndex = index
				this.$router.push({
					name: 'home'
				});
			},
			centerChange(index) {
				this.itemMenu = [{
					"buttons": ["新增", "查看", "修改", "删除"],
					"menu": "修改密码",
					"tableName": "updatePassword"
				}, {
					"buttons": ["新增", "查看", "修改", "删除"],
					"menu": "个人信息",
					"tableName": "center"
				}];
				this.title = ""
				this.currentIndex = index
				this.$router.push({
					name: 'home'
				});
			},
			refreshContent() {
				// 刷新当前页面内容
				this.$router.go(0)
			}
		}
	};
</script>
<style lang="scss" scoped>
	.main-content {
		background-color: #F6F8FA;
		padding: 0;
		display: flex;
		flex-direction: column;
		height: 100%;

		.content-header {
			background: #fff;
			padding: 16px 24px;
			border-bottom: 1px solid #e9eef3;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

			.bread-crumbs {
				flex: 1;
			}

			.page-actions {
				display: flex;
				gap: 8px;
			}
		}

		.content-body {
			flex: 1;
			padding: 24px;
			overflow-y: auto;

			.router-view {
				background: #FFFFFF;
				border-radius: 8px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
				min-height: calc(100vh - 200px);
				padding: 24px;
				box-sizing: border-box;
			}
		}
	}

	// 保留原有样式以兼容
	a {
		text-decoration: none;
		color: #555;
	}

	a:hover {
		background: #1E90FF;
		color: #fff;
	}

	.nav-list {
		width: 100%;
		margin: 0 auto;
		text-align: left;
		margin-top: 20px;

		.nav-title {
			display: inline-block;
			font-size: 15px;
			color: #333;
			padding: 15px 25px;
			border: none;
			border-radius: 6px;
			transition: all 0.3s ease;

			&:hover {
				background-color: #f5f7fa;
			}
		}

		.nav-title.active {
			color: #1E90FF;
			cursor: default;
			background-color: #e6f7ff;
			font-weight: 600;
		}
	}

	.nav-item {
		margin-top: 20px;
		background: #FFFFFF;
		padding: 15px 0;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

		.menu {
			padding: 15px 25px;
			transition: all 0.3s ease;

			&:hover {
				background-color: #f5f7fa;
			}
		}
	}
</style>
