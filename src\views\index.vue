<template>
	<el-container>
		<index-header></index-header>
		<el-container>
			<index-aside></index-aside>
			<index-main></index-main>
		</el-container>
	</el-container>
</template>
<script>
	import IndexHeader from '@/components/index/IndexHeader'
	import IndexAside from '@/components/index/IndexAsideStatic'
	import IndexMain from '@/components/index/IndexMain'
	export default {
		components: {
			IndexHeader,
			IndexAside,
			IndexMain
		}
	}
</script>

<style lang="scss" scoped>
	// 铺满全屏
	.el-container {
		position: absolute;
		width: 100%;
		top: 0;
		left: 0;
		bottom: 0;
		display: flex;
		flex-direction: column;

		// 嵌套容器
		.el-container {
			position: relative;
			flex: 1;
			display: flex;
			flex-direction: row;
		}

		// 响应式设计
		@media (max-width: 768px) {
			.el-aside {
				position: fixed;
				z-index: 1000;
				height: 100vh;
				top: 60px;
				left: -210px;
				transition: left 0.3s ease;

				&.mobile-show {
					left: 0;
				}
			}

			.el-main {
				margin-left: 0 !important;
			}
		}
	}
</style>
