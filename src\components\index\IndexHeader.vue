<template>
    <div class="navbar navbar-enhanced" :style="{backgroundColor:heads.headBgColor,height:heads.headHeight,boxShadow:heads.headBoxShadow,lineHeight:heads.headHeight}">
        <!-- 左侧区域：Logo + 系统名称 + 菜单收缩按钮 -->
        <div class="navbar-left">
            <div class="menu-toggle" @click="toggleSidebar">
                <i class="el-icon-s-fold" v-if="!sidebarCollapsed"></i>
                <i class="el-icon-s-unfold" v-else></i>
            </div>
            <div class="title-section">
                <el-image v-if="heads.headTitleImg" class="title-img" :style="{width:heads.headTitleImgWidth,height:heads.headTitleImgHeight,boxShadow:heads.headTitleImgBoxShadow,borderRadius:heads.headTitleImgBorderRadius}" :src="heads.headTitleImgUrl" fit="cover"></el-image>
                <div class="title-name" :style="{color:heads.headFontColor,fontSize:heads.headFontSize}">{{this.$project.projectName}}</div>
            </div>
        </div>

        <!-- 中间区域：快捷操作 -->
        <div class="navbar-center">
            <div class="quick-actions">
                <el-tooltip content="刷新页面" placement="bottom">
                    <div class="action-item" @click="refreshPage">
                        <i class="el-icon-refresh"></i>
                    </div>
                </el-tooltip>
                <el-tooltip content="全屏显示" placement="bottom">
                    <div class="action-item" @click="toggleFullscreen">
                        <i class="el-icon-full-screen"></i>
                    </div>
                </el-tooltip>
            </div>
        </div>

        <!-- 右侧区域：通知 + 用户信息 + 操作菜单 -->
        <div class="navbar-right">
            <!-- 通知中心 -->
            <el-dropdown class="notification-dropdown" trigger="click">
                <div class="notification-bell">
                    <i class="el-icon-bell"></i>
                    <span class="notification-badge" v-if="notificationCount > 0">{{notificationCount}}</span>
                </div>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>
                        <div class="notification-header">
                            <span>通知中心</span>
                            <el-button type="text" size="mini">全部已读</el-button>
                        </div>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                        <div class="notification-item">
                            <i class="el-icon-info"></i>
                            <span>系统消息：欢迎使用管理系统</span>
                        </div>
                    </el-dropdown-item>
                    <el-dropdown-item>
                        <div class="notification-footer">
                            <el-button type="text" size="mini">查看全部</el-button>
                        </div>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>

            <!-- 用户信息下拉菜单 -->
            <el-dropdown class="user-dropdown" trigger="click">
                <div class="user-info-section">
                    <div class="user-avatar">
                        <i class="el-icon-user-solid"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name">{{this.$storage.get('adminName')}}</div>
                        <div class="user-role">{{this.$storage.get('role')}}</div>
                    </div>
                    <i class="el-icon-arrow-down"></i>
                </div>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="goToProfile">
                        <i class="el-icon-user"></i>
                        个人信息
                    </el-dropdown-item>
                    <el-dropdown-item @click.native="goToSettings">
                        <i class="el-icon-setting"></i>
                        系统设置
                    </el-dropdown-item>
                    <el-dropdown-item divided @click.native="onIndexTap">
                        <i class="el-icon-switch-button"></i>
                        退出到前台
                    </el-dropdown-item>
                    <el-dropdown-item @click.native="onLogout">
                        <i class="el-icon-circle-close"></i>
                        退出登录
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                dialogVisible: false,
                ruleForm: {},
                user: {},
                heads: {"headLogoutFontHoverColor":"#fff","headFontSize":"20px","headUserInfoFontColor":"rgba(255, 255, 255, 1)","headBoxShadow":"0 11px 0px #343957","headTitleImgHeight":"44px","headLogoutFontHoverBgColor":"rgba(38, 155, 158, 1)","headFontColor":"rgba(255, 255, 255, 1)","headTitleImg":false,"headHeight":"60px","headTitleImgBorderRadius":"22px","headTitleImgUrl":"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg","headBgColor":"#1E90FF","headTitleImgBoxShadow":"0 1px 6px #444","headLogoutFontColor":"rgba(255, 255, 255, 1)","headUserInfoFontSize":"16px","headTitleImgWidth":"44px","headTitleStyle":"1","headLogoutFontSize":"16px"},
                // 新增的数据属性
                sidebarCollapsed: false,
                notificationCount: 3,
                isFullscreen: false
            };
        },
        created() {
            this.setHeaderStyle()
        },
        mounted() {
            let sessionTable = this.$storage.get("sessionTable")
            this.$http({
                url: sessionTable + '/session',
                method: "get"
            }).then(({
                         data
                     }) => {
                if (data && data.code === 0) {
                    this.user = data.data;
                } else {
                    let message = this.$message
                    message.error(data.msg);
                }
            });
        },
        methods: {
            onLogout() {
                let storage = this.$storage
                let router = this.$router
                storage.clear()
                router.replace({
                    name: "login"
                });
            },
            onIndexTap(){
                window.location.href = `${this.$base.indexUrl}`
            },
            setHeaderStyle() {
                this.$nextTick(()=>{
                    document.querySelectorAll('.navbar .right-menu .logout').forEach(el=>{
                        el.addEventListener("mouseenter", e => {
                            e.stopPropagation()
                            el.style.backgroundColor = this.heads.headLogoutFontHoverBgColor
                            el.style.color = this.heads.headLogoutFontHoverColor
                        })
                        el.addEventListener("mouseleave", e => {
                            e.stopPropagation()
                            el.style.backgroundColor = "transparent"
                            el.style.color = this.heads.headLogoutFontColor
                        })
                    })
                })
            },
            // 新增的方法
            toggleSidebar() {
                this.sidebarCollapsed = !this.sidebarCollapsed
                // 触发事件通知父组件或其他组件
                this.$emit('sidebar-toggle', this.sidebarCollapsed)
                // 也可以通过事件总线或Vuex来管理状态
                this.$root.$emit('sidebar-toggle', this.sidebarCollapsed)
            },

            refreshPage() {
                this.$router.go(0)
            },
            toggleFullscreen() {
                if (!this.isFullscreen) {
                    // 进入全屏
                    const element = document.documentElement
                    if (element.requestFullscreen) {
                        element.requestFullscreen()
                    } else if (element.webkitRequestFullscreen) {
                        element.webkitRequestFullscreen()
                    } else if (element.mozRequestFullScreen) {
                        element.mozRequestFullScreen()
                    } else if (element.msRequestFullscreen) {
                        element.msRequestFullscreen()
                    }
                } else {
                    // 退出全屏
                    if (document.exitFullscreen) {
                        document.exitFullscreen()
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen()
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen()
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen()
                    }
                }
                this.isFullscreen = !this.isFullscreen
            },
            goToProfile() {
                this.$router.push('/center')
            },
            goToSettings() {
                // 跳转到系统设置页面
                this.$message.info('系统设置功能开发中...')
            }
        }
    };
</script>


<style lang="scss" scoped>
    .navbar {
        height: 60px;
        width: 100%;
        padding: 0 24px;
        box-sizing: border-box;
        background: linear-gradient(135deg, #1E90FF 0%, #4169E1 50%, #0066CC 100%);
        position: relative;
        z-index: 111;
        box-shadow: 0 2px 12px rgba(30, 144, 255, 0.3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;

        // 左侧区域
        .navbar-left {
            display: flex;
            align-items: center;
            gap: 16px;

            .menu-toggle {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.2);

                &:hover {
                    background: rgba(255, 255, 255, 0.2);
                    transform: scale(1.05);
                }

                i {
                    font-size: 18px;
                    color: #fff;
                }
            }

            .title-section {
                display: flex;
                align-items: center;
                gap: 12px;

                .title-img {
                    width: 40px;
                    height: 40px;
                    border-radius: 20px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    transition: all 0.3s ease;

                    &:hover {
                        transform: scale(1.05);
                        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
                    }
                }

                .title-name {
                    font-size: 20px;
                    color: #fff;
                    font-weight: 700;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                    letter-spacing: 1px;
                    transition: all 0.3s ease;

                    &:hover {
                        text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
                    }
                }
            }
        }

        // 中间区域
        .navbar-center {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;

            .quick-actions {
                display: flex;
                gap: 8px;

                .action-item {
                    width: 36px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border: 1px solid rgba(255, 255, 255, 0.2);

                    &:hover {
                        background: rgba(255, 255, 255, 0.2);
                        transform: translateY(-2px);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    }

                    i {
                        font-size: 16px;
                        color: #fff;
                    }
                }
            }
        }

        // 右侧区域
        .navbar-right {
            display: flex;
            align-items: center;
            gap: 16px;

            .notification-dropdown {
                .notification-bell {
                    position: relative;
                    width: 40px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border: 1px solid rgba(255, 255, 255, 0.2);

                    &:hover {
                        background: rgba(255, 255, 255, 0.2);
                        transform: scale(1.05);
                    }

                    i {
                        font-size: 18px;
                        color: #fff;
                    }

                    .notification-badge {
                        position: absolute;
                        top: -5px;
                        right: -5px;
                        background: #ff4757;
                        color: #fff;
                        border-radius: 10px;
                        padding: 2px 6px;
                        font-size: 12px;
                        min-width: 18px;
                        height: 18px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: 2px solid #fff;
                    }
                }
            }

            .user-dropdown {
                .user-info-section {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    padding: 8px 16px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 25px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border: 1px solid rgba(255, 255, 255, 0.2);

                    &:hover {
                        background: rgba(255, 255, 255, 0.2);
                        transform: translateY(-1px);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    }

                    .user-avatar {
                        width: 32px;
                        height: 32px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 16px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        i {
                            font-size: 16px;
                            color: #fff;
                        }
                    }

                    .user-details {
                        .user-name {
                            font-size: 14px;
                            color: #fff;
                            font-weight: 600;
                            line-height: 1.2;
                        }

                        .user-role {
                            font-size: 12px;
                            color: rgba(255, 255, 255, 0.8);
                            line-height: 1.2;
                        }
                    }

                    .el-icon-arrow-down {
                        font-size: 12px;
                        color: rgba(255, 255, 255, 0.8);
                        transition: transform 0.3s ease;
                    }

                    &:hover .el-icon-arrow-down {
                        transform: rotate(180deg);
                    }
                }
            }
        }
    }

    // 下拉菜单样式
    .notification-header,
    .notification-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
    }

    .notification-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;

        i {
            color: #1E90FF;
        }
    }
</style>
