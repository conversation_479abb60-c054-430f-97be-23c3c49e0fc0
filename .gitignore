# Dependencies
node_modules
npm-debug.log
yarn-error.log
yarn-debug.log
package-lock.json
yarn.lock
pnpm-lock.yaml

# Build outputs
dist
build
out
.output
.nuxt
.cache
.temp

# IDE and editors
.idea
.vscode
*.swp
*.swo
.DS_Store
Thumbs.db
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Coverage directory
coverage
.nyc_output

# Testing
/cypress/videos/
/cypress/screenshots/
/e2e/videos/
/e2e/screenshots/

# Temporary files
.tmp
.temp
.cache
*.tmp
*.temp

# Generated files
.generated
generated
auto-imports.d.ts
components.d.ts

# Debug
.debug

# Local files
*.local

# Compiled binary addons
/build/Release
