<!-- PrivacyInfo.vue -->
<template>
  <div class="privacy-info">
    <span class="masked-text">{{ maskedText }}</span>
  </div>
</template>

<script>
export default {
  name: "PrivacyInfo",
  props: {
    value: {
      type: [String, Number],
      required: true
    },
    type: {
      type: String,
      default: 'default', // 可选值：'phone', 'idcard', 'email', 'default'
      validator: function(value) {
        return ['phone', 'idcard', 'email', 'default'].indexOf(value) !== -1
      }
    }
  },
  data() {
    return {
      maskedText: ''
    }
  },
  created() {
    this.maskedText = this.getMaskedText();
    console.log(`PrivacyInfo组件创建: 值=${this.value}, 掩码=${this.maskedText}`);
  },
  watch: {
    value: {
      handler(newVal) {
        this.maskedText = this.getMaskedText();
      },
      immediate: true
    }
  },
  methods: {
    getMaskedText() {
      const val = String(this.value || '');
      if (!val) return '';
      
      switch (this.type) {
        case 'phone':
          // 手机号：显示前3位和后4位，中间用星号代替
          if (val.length >= 11) {
            return val.substring(0, 3) + '****' + val.substring(val.length - 4);
          } else if (val.length > 2) {
            return val.substring(0, 1) + '*'.repeat(val.length - 2) + val.substring(val.length - 1);
          }
          return '*'.repeat(val.length);
        
        case 'idcard':
          // 身份证号：显示前4位和后4位，中间用星号代替
          if (val.length >= 15) {
            return val.substring(0, 4) + '*'.repeat(val.length - 8) + val.substring(val.length - 4);
          } else if (val.length > 2) {
            return val.substring(0, 1) + '*'.repeat(val.length - 2) + val.substring(val.length - 1);
          }
          return '*'.repeat(val.length);
        
        case 'email':
          // 邮箱：显示@之前的第一个字符和@之后的所有内容，其余用星号代替
          const atIndex = val.indexOf('@');
          if (atIndex > 0) {
            const prefix = val.substring(0, atIndex);
            const suffix = val.substring(atIndex);
            return prefix.substring(0, 1) + '*'.repeat(prefix.length - 1) + suffix;
          }
          return val.substring(0, 1) + '*'.repeat(val.length - 1);
        
        case 'default':
        default:
          // 默认：显示前1/3和后1/3，中间用星号代替
          const len = val.length;
          if (len <= 2) return '*'.repeat(len);
          
          const frontLen = Math.max(Math.floor(len / 3), 1);
          const endLen = Math.max(Math.floor(len / 3), 1);
          return val.substring(0, frontLen) + '*'.repeat(len - frontLen - endLen) + val.substring(len - endLen);
      }
    }
  }
}
</script>

<style scoped>
.privacy-info {
  display: inline-block;
  width: 100%;
}
.masked-text {
  color: #333;
}
</style> 